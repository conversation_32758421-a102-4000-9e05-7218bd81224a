.format-converter {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  margin: 16px 0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.converter-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e5e7eb;
}

.converter-icon {
  width: 20px;
  height: 20px;
  color: #6b7280;
}

.converter-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
}

.converter-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.file-info {
  display: flex;
  align-items: center;
  padding: 12px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: #f9fafb;
}

.file-details {
  flex: 1;
}

.file-name {
  font-weight: 500;
  color: #374151;
  margin-bottom: 4px;
}

.file-meta {
  display: flex;
  gap: 12px;
  font-size: 14px;
  color: #6b7280;
}

.file-format {
  background: #ddd6fe;
  color: #7c3aed;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
  font-size: 12px;
}

.conversion-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.conversion-arrow {
  display: flex;
  justify-content: center;
  align-items: center;
}

.arrow-icon {
  width: 24px;
  height: 24px;
  color: #6b7280;
}

.conversion-controls {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.conversion-info {
  display: flex;
  gap: 12px;
  padding: 12px;
  background: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: 6px;
}

.warning-icon {
  width: 20px;
  height: 20px;
  color: #f59e0b;
  flex-shrink: 0;
  margin-top: 2px;
}

.conversion-info p {
  margin: 0;
  font-size: 14px;
  color: #92400e;
  line-height: 1.4;
}

.convert-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 10px 16px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.convert-button:hover:not(:disabled) {
  background: #2563eb;
}

.convert-button:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

.button-icon {
  width: 16px;
  height: 16px;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.conversion-progress {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 16px;
  background: #eff6ff;
  border: 1px solid #3b82f6;
  border-radius: 6px;
}

.progress-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #1d4ed8;
  font-weight: 500;
}

.progress-icon {
  width: 16px;
  height: 16px;
  animation: spin 1s linear infinite;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background: #dbeafe;
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #3b82f6;
  border-radius: 2px;
  animation: progress 2s ease-in-out infinite;
}

@keyframes progress {
  0% { width: 0%; }
  50% { width: 70%; }
  100% { width: 100%; }
}

.conversion-success {
  display: flex;
  gap: 12px;
  padding: 16px;
  background: #f0fdf4;
  border: 1px solid #22c55e;
  border-radius: 6px;
}

.success-icon {
  width: 20px;
  height: 20px;
  color: #22c55e;
  flex-shrink: 0;
  margin-top: 2px;
}

.success-details {
  flex: 1;
}

.success-message {
  margin: 0 0 8px 0;
  color: #166534;
  font-weight: 500;
}

.converted-file-info {
  margin-bottom: 12px;
}

.converted-filename {
  font-weight: 500;
  color: #374151;
  margin-bottom: 2px;
}

.converted-size {
  font-size: 14px;
  color: #6b7280;
}

.download-converted-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: #22c55e;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.download-converted-button:hover {
  background: #16a34a;
}

.already-converted {
  display: flex;
  gap: 12px;
  padding: 16px;
  background: #f0fdf4;
  border: 1px solid #22c55e;
  border-radius: 6px;
  align-items: center;
}

.already-compatible {
  display: flex;
  gap: 12px;
  padding: 16px;
  background: #f0fdf4;
  border: 1px solid #22c55e;
  border-radius: 6px;
  align-items: center;
}

.already-compatible p {
  margin: 0;
  color: #166534;
  font-weight: 500;
}

.conversion-error {
  display: flex;
  gap: 12px;
  padding: 16px;
  background: #fef2f2;
  border: 1px solid #ef4444;
  border-radius: 6px;
}

.error-icon {
  width: 20px;
  height: 20px;
  color: #ef4444;
  flex-shrink: 0;
  margin-top: 2px;
}

.error-message {
  margin: 0 0 8px 0;
  color: #dc2626;
  font-weight: 500;
}

.retry-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.retry-button:hover {
  background: #dc2626;
}

/* Responsive design */
@media (max-width: 768px) {
  .format-converter {
    padding: 16px;
    margin: 12px 0;
  }
  
  .conversion-info {
    flex-direction: column;
    gap: 8px;
  }
  
  .file-meta {
    flex-direction: column;
    gap: 4px;
  }
}
