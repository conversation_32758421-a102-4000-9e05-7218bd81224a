// Fixed Archive Playback with proper video codec support
import React from 'react';
import FixedArchivePlayback from './FixedArchivePlayback';
import './ArchivePlaybackContent.css';

const ArchivePlaybackContent = ({ onSelectRecording, selectedRecordingId }) => {
  return (
    <FixedArchivePlayback
      onSelectRecording={onSelectRecording}
      selectedRecordingId={selectedRecordingId}
    />
  );
};

export default React.memo(ArchivePlaybackContent);
