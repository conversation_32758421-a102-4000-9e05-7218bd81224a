import React, { useState } from 'react';
import { 
  RefreshCw, 
  Download, 
  AlertCircle, 
  CheckCircle, 
  Clock,
  FileVideo,
  ArrowRight
} from 'lucide-react';
import { API_BASE_URL } from '../../utils/apiConfig';
import './FormatConverter.css';

const FormatConverter = ({ recording, onConversionComplete }) => {
  const [isConverting, setIsConverting] = useState(false);
  const [conversionStatus, setConversionStatus] = useState(null);
  const [error, setError] = useState(null);

  // Check if file needs conversion (WebM or other non-MP4 formats)
  const needsConversion = recording && !recording.filename.toLowerCase().endsWith('.mp4');
  
  // Check if file is already converted
  const convertedFilename = recording?.filename.replace(/\.[^.]+$/, '_converted.mp4');
  const hasConvertedVersion = recording?.converted_available || false;

  const handleConvert = async () => {
    if (!recording) return;

    setIsConverting(true);
    setError(null);
    setConversionStatus(null);

    try {
      const response = await fetch(
        `${API_BASE_URL}/api/archive/convert/${recording.stream_id}/${recording.filename}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      const data = await response.json();

      if (response.ok && data.success) {
        setConversionStatus({
          success: true,
          message: data.message,
          convertedFilename: data.converted_filename,
          convertedUrl: data.converted_url,
          fileSize: data.file_size
        });

        // Notify parent component
        if (onConversionComplete) {
          onConversionComplete({
            originalRecording: recording,
            convertedFilename: data.converted_filename,
            convertedUrl: data.converted_url
          });
        }
      } else {
        throw new Error(data.detail || 'Conversion failed');
      }
    } catch (err) {
      console.error('Conversion error:', err);
      setError(err.message);
    } finally {
      setIsConverting(false);
    }
  };

  const handleDownloadConverted = () => {
    if (conversionStatus?.convertedUrl) {
      window.open(`${API_BASE_URL}${conversionStatus.convertedUrl}`, '_blank');
    }
  };

  const formatFileSize = (bytes) => {
    if (!bytes) return 'Unknown size';
    const mb = bytes / (1024 * 1024);
    return `${mb.toFixed(1)} MB`;
  };

  const getFileExtension = (filename) => {
    return filename.split('.').pop().toUpperCase();
  };

  if (!recording) {
    return null;
  }

  return (
    <div className="format-converter">
      <div className="converter-header">
        <FileVideo className="converter-icon" />
        <h3>Format Converter</h3>
      </div>

      <div className="converter-content">
        {/* Original file info */}
        <div className="file-info original-file">
          <div className="file-details">
            <div className="file-name">{recording.filename}</div>
            <div className="file-meta">
              <span className="file-format">{getFileExtension(recording.filename)}</span>
              <span className="file-size">{formatFileSize(recording.file_size)}</span>
            </div>
          </div>
        </div>

        {/* Conversion status */}
        {needsConversion && (
          <div className="conversion-section">
            <div className="conversion-arrow">
              <ArrowRight className="arrow-icon" />
            </div>

            {/* Conversion controls */}
            {!conversionStatus && !hasConvertedVersion && (
              <div className="conversion-controls">
                <div className="conversion-info">
                  <AlertCircle className="warning-icon" />
                  <div>
                    <p>This file format may not be compatible with all browsers.</p>
                    <p>Convert to MP4 for better compatibility.</p>
                  </div>
                </div>
                
                <button
                  onClick={handleConvert}
                  disabled={isConverting}
                  className="convert-button"
                >
                  {isConverting ? (
                    <>
                      <RefreshCw className="button-icon spinning" />
                      Converting...
                    </>
                  ) : (
                    <>
                      <RefreshCw className="button-icon" />
                      Convert to MP4
                    </>
                  )}
                </button>
              </div>
            )}

            {/* Conversion progress */}
            {isConverting && (
              <div className="conversion-progress">
                <div className="progress-indicator">
                  <Clock className="progress-icon" />
                  <span>Converting to browser-compatible format...</span>
                </div>
                <div className="progress-bar">
                  <div className="progress-fill"></div>
                </div>
              </div>
            )}

            {/* Conversion success */}
            {conversionStatus?.success && (
              <div className="conversion-success">
                <CheckCircle className="success-icon" />
                <div className="success-details">
                  <p className="success-message">{conversionStatus.message}</p>
                  <div className="converted-file-info">
                    <div className="converted-filename">{conversionStatus.convertedFilename}</div>
                    <div className="converted-size">{formatFileSize(conversionStatus.fileSize)}</div>
                  </div>
                  <button
                    onClick={handleDownloadConverted}
                    className="download-converted-button"
                  >
                    <Download className="button-icon" />
                    Download MP4
                  </button>
                </div>
              </div>
            )}

            {/* Already converted */}
            {hasConvertedVersion && !conversionStatus && (
              <div className="already-converted">
                <CheckCircle className="success-icon" />
                <div>
                  <p>MP4 version available</p>
                  <button
                    onClick={() => window.open(`${API_BASE_URL}/api/archive/stream/${recording.stream_id}/${convertedFilename}`, '_blank')}
                    className="download-converted-button"
                  >
                    <Download className="button-icon" />
                    Download MP4
                  </button>
                </div>
              </div>
            )}

            {/* Conversion error */}
            {error && (
              <div className="conversion-error">
                <AlertCircle className="error-icon" />
                <div>
                  <p className="error-message">Conversion failed: {error}</p>
                  <button
                    onClick={handleConvert}
                    className="retry-button"
                  >
                    <RefreshCw className="button-icon" />
                    Retry
                  </button>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Already MP4 */}
        {!needsConversion && (
          <div className="already-compatible">
            <CheckCircle className="success-icon" />
            <p>This file is already in MP4 format and browser-compatible.</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default FormatConverter;
