#!/usr/bin/env python3
"""
Test script for format conversion functionality

This script demonstrates how to use the format conversion features
and tests the conversion API endpoints.
"""

import requests
import json
import sys
from pathlib import Path

API_BASE_URL = "http://localhost:8000"

def test_conversion_api():
    """Test the conversion API endpoint"""
    print("🧪 Testing Format Conversion API")
    print("=" * 50)
    
    # Test with existing MP4 file
    stream_id = "Eagle_192.168.4.242"
    filename = "test_recording_2.mp4"
    
    print(f"Testing conversion for: {stream_id}/{filename}")
    
    try:
        response = requests.get(f"{API_BASE_URL}/api/archive/convert/{stream_id}/{filename}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Conversion API Response:")
            print(json.dumps(data, indent=2))
        else:
            print(f"❌ API Error: {response.status_code}")
            print(response.text)
            
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to API server. Make sure the backend is running on port 8000.")
        return False
    except Exception as e:
        print(f"❌ Error testing API: {e}")
        return False
    
    return True

def test_supported_formats():
    """Test which formats are supported for conversion"""
    print("\n🎬 Supported Format Information")
    print("=" * 50)
    
    supported_input_formats = [
        ".webm", ".avi", ".mov", ".mkv", ".flv", ".wmv", ".m4v", ".3gp"
    ]
    
    print("Input formats that can be converted to MP4:")
    for fmt in supported_input_formats:
        print(f"  ✓ {fmt}")
    
    print("\nOutput format:")
    print("  ✓ .mp4 (H.264 video + AAC audio)")
    
    print("\nBrowser compatibility:")
    print("  ✓ Chrome, Firefox, Safari, Edge")
    print("  ✓ Mobile browsers (iOS Safari, Chrome Mobile)")
    print("  ✓ HTML5 video element with range requests")

def demonstrate_conversion_benefits():
    """Show the benefits of format conversion"""
    print("\n💡 Format Conversion Benefits")
    print("=" * 50)
    
    benefits = [
        "🌐 Universal browser compatibility",
        "📱 Mobile device support", 
        "⚡ Faster video seeking/scrubbing",
        "🎯 Optimized for web streaming",
        "🔧 Consistent codec support",
        "📊 Better compression efficiency",
        "🚀 Reduced bandwidth usage"
    ]
    
    for benefit in benefits:
        print(f"  {benefit}")

def show_usage_examples():
    """Show usage examples for the conversion tools"""
    print("\n📖 Usage Examples")
    print("=" * 50)
    
    print("1. Convert single file via API:")
    print("   GET /api/archive/convert/{stream_id}/{filename}")
    print("   Example: GET /api/archive/convert/Eagle_192.168.4.242/recording.webm")
    
    print("\n2. Convert single file via command line:")
    print("   python convert_recordings.py input.webm output.mp4")
    
    print("\n3. Batch convert directory:")
    print("   python convert_recordings.py --batch recordings/")
    
    print("\n4. Scan and convert all recordings:")
    print("   python convert_recordings.py --scan-recordings")
    
    print("\n5. Frontend integration:")
    print("   The FormatConverter component automatically detects")
    print("   incompatible formats and offers conversion options.")

def check_ffmpeg_availability():
    """Check if FFmpeg is available"""
    print("\n🔧 FFmpeg Availability Check")
    print("=" * 50)
    
    # Check bundled FFmpeg
    current_dir = Path(__file__).parent
    ffmpeg_bundled = current_dir / "ffmpeg-master-latest-win64-gpl-shared" / "bin" / "ffmpeg.exe"
    
    if ffmpeg_bundled.exists():
        print("✅ Bundled FFmpeg found:")
        print(f"   {ffmpeg_bundled}")
        return True
    
    # Check system FFmpeg
    import shutil
    ffmpeg_system = shutil.which("ffmpeg")
    if ffmpeg_system:
        print("✅ System FFmpeg found:")
        print(f"   {ffmpeg_system}")
        return True
    
    print("❌ FFmpeg not found!")
    print("   Please ensure FFmpeg is installed or available in the bundled directory.")
    return False

def main():
    """Main test function"""
    print("🎥 VMS Format Conversion Test Suite")
    print("=" * 60)
    
    # Check FFmpeg
    if not check_ffmpeg_availability():
        print("\n⚠️  FFmpeg is required for format conversion to work.")
        return 1
    
    # Test API
    if not test_conversion_api():
        print("\n⚠️  API tests failed. Make sure the backend server is running.")
        return 1
    
    # Show information
    test_supported_formats()
    demonstrate_conversion_benefits()
    show_usage_examples()
    
    print("\n🎉 All tests completed successfully!")
    print("\nYour VMS system now supports:")
    print("  • Automatic format detection")
    print("  • Browser-compatible MP4 conversion") 
    print("  • WebM, AVI, MOV, and other format support")
    print("  • Both API and command-line conversion tools")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
