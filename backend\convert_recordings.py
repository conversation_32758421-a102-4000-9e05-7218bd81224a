#!/usr/bin/env python3
"""
Recording Format Converter Utility

This script converts video recordings to browser-compatible MP4 format.
Supports conversion from WebM, AVI, MOV, and other formats to MP4 with H.264/AAC codecs.

Usage:
    python convert_recordings.py [input_file] [output_file]
    python convert_recordings.py --batch [directory]
    python convert_recordings.py --scan-recordings

Examples:
    # Convert single file
    python convert_recordings.py test_recording_2.webm test_recording_2.mp4
    
    # Batch convert all WebM files in recordings directory
    python convert_recordings.py --batch recordings/
    
    # Scan and convert all non-MP4 files in recordings directory
    python convert_recordings.py --scan-recordings
"""

import os
import sys
import subprocess
import shutil
import argparse
import logging
from pathlib import Path
from typing import List, Optional

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def get_ffmpeg_path() -> Optional[str]:
    """Get the path to FFmpeg executable"""
    # Check if FFmpeg is in the bundled directory
    current_dir = Path(__file__).parent
    ffmpeg_bundled = current_dir / "ffmpeg-master-latest-win64-gpl-shared" / "bin" / "ffmpeg.exe"
    
    if ffmpeg_bundled.exists():
        return str(ffmpeg_bundled)
    
    # Check if FFmpeg is in system PATH
    ffmpeg_system = shutil.which("ffmpeg")
    if ffmpeg_system:
        return ffmpeg_system
    
    return None

def convert_to_mp4(input_path: Path, output_path: Path, ffmpeg_path: str) -> bool:
    """
    Convert a video file to browser-compatible MP4 format
    
    Args:
        input_path: Path to input video file
        output_path: Path for output MP4 file
        ffmpeg_path: Path to FFmpeg executable
        
    Returns:
        True if conversion successful, False otherwise
    """
    try:
        # Create temporary output path
        temp_output_path = str(output_path) + ".tmp"
        
        # Enhanced FFmpeg command for maximum compatibility
        cmd = [
            ffmpeg_path,
            "-i", str(input_path),
            "-c:v", "libx264",      # H.264 codec (widely supported)
            "-c:a", "aac",          # AAC audio codec (universal compatibility)
            "-preset", "fast",       # Fast encoding
            "-crf", "23",           # Good quality
            "-pix_fmt", "yuv420p",  # Pixel format compatible with most players
            "-movflags", "+faststart",  # Optimize for web streaming
            "-ar", "44100",         # Standard audio sample rate
            "-b:a", "128k",         # Audio bitrate
            "-avoid_negative_ts", "make_zero",  # Handle timestamp issues
            "-y",                   # Overwrite output file
            temp_output_path
        ]

        logger.info(f"Converting {input_path.name} to MP4...")
        logger.debug(f"FFmpeg command: {' '.join(cmd)}")

        # Run FFmpeg conversion
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=600  # 10 minute timeout
        )

        if result.returncode == 0:
            # Move temporary file to final location
            os.rename(temp_output_path, str(output_path))
            
            # Get file sizes for comparison
            input_size = input_path.stat().st_size
            output_size = output_path.stat().st_size
            
            logger.info(f"✓ Successfully converted {input_path.name}")
            logger.info(f"  Input size: {input_size / (1024*1024):.1f} MB")
            logger.info(f"  Output size: {output_size / (1024*1024):.1f} MB")
            
            return True
        else:
            # Clean up temporary file if it exists
            if os.path.exists(temp_output_path):
                os.remove(temp_output_path)
            
            logger.error(f"✗ Conversion failed for {input_path.name}")
            logger.error(f"FFmpeg error: {result.stderr}")
            return False

    except subprocess.TimeoutExpired:
        # Clean up temporary file
        if os.path.exists(temp_output_path):
            os.remove(temp_output_path)
        
        logger.error(f"✗ Conversion timeout for {input_path.name} - file may be too large")
        return False
    except Exception as e:
        # Clean up temporary file
        if os.path.exists(temp_output_path):
            os.remove(temp_output_path)
        
        logger.error(f"✗ Error converting {input_path.name}: {e}")
        return False

def find_video_files(directory: Path, extensions: List[str] = None) -> List[Path]:
    """
    Find video files in directory that need conversion
    
    Args:
        directory: Directory to search
        extensions: List of file extensions to look for
        
    Returns:
        List of video file paths
    """
    if extensions is None:
        extensions = ['.webm', '.avi', '.mov', '.mkv', '.flv', '.wmv']
    
    video_files = []
    
    for ext in extensions:
        pattern = f"**/*{ext}"
        files = list(directory.glob(pattern))
        video_files.extend(files)
    
    return sorted(video_files)

def convert_single_file(input_file: str, output_file: str = None) -> bool:
    """Convert a single video file to MP4"""
    ffmpeg_path = get_ffmpeg_path()
    if not ffmpeg_path:
        logger.error("FFmpeg not found. Please install FFmpeg or ensure it's in your PATH.")
        return False
    
    input_path = Path(input_file)
    if not input_path.exists():
        logger.error(f"Input file not found: {input_file}")
        return False
    
    # Generate output filename if not provided
    if output_file is None:
        output_file = str(input_path.with_suffix('.mp4'))
        if output_file == str(input_path):
            output_file = str(input_path.with_stem(input_path.stem + '_converted'))
    
    output_path = Path(output_file)
    
    # Check if output file already exists
    if output_path.exists():
        response = input(f"Output file {output_file} already exists. Overwrite? (y/N): ")
        if response.lower() != 'y':
            logger.info("Conversion cancelled.")
            return False
    
    return convert_to_mp4(input_path, output_path, ffmpeg_path)

def batch_convert_directory(directory: str, extensions: List[str] = None) -> int:
    """Convert all video files in a directory to MP4"""
    ffmpeg_path = get_ffmpeg_path()
    if not ffmpeg_path:
        logger.error("FFmpeg not found. Please install FFmpeg or ensure it's in your PATH.")
        return 0
    
    dir_path = Path(directory)
    if not dir_path.exists():
        logger.error(f"Directory not found: {directory}")
        return 0
    
    video_files = find_video_files(dir_path, extensions)
    
    if not video_files:
        logger.info("No video files found for conversion.")
        return 0
    
    logger.info(f"Found {len(video_files)} video files to convert")
    
    converted_count = 0
    for video_file in video_files:
        # Generate output filename
        output_file = video_file.with_suffix('.mp4')
        if output_file == video_file:
            output_file = video_file.with_stem(video_file.stem + '_converted')
        
        # Skip if output already exists
        if output_file.exists():
            logger.info(f"Skipping {video_file.name} - output already exists")
            continue
        
        if convert_to_mp4(video_file, output_file, ffmpeg_path):
            converted_count += 1
    
    logger.info(f"Conversion complete. {converted_count}/{len(video_files)} files converted successfully.")
    return converted_count

def scan_recordings_directory() -> int:
    """Scan the recordings directory and convert all non-MP4 files"""
    recordings_dir = Path("recordings")
    if not recordings_dir.exists():
        logger.error("Recordings directory not found. Please run this script from the backend directory.")
        return 0
    
    logger.info("Scanning recordings directory for files that need conversion...")
    return batch_convert_directory(str(recordings_dir))

def main():
    parser = argparse.ArgumentParser(
        description="Convert video recordings to browser-compatible MP4 format",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__
    )
    
    parser.add_argument("input_file", nargs="?", help="Input video file to convert")
    parser.add_argument("output_file", nargs="?", help="Output MP4 file (optional)")
    parser.add_argument("--batch", metavar="DIR", help="Batch convert all video files in directory")
    parser.add_argument("--scan-recordings", action="store_true", help="Scan and convert recordings directory")
    parser.add_argument("--extensions", nargs="+", default=['.webm', '.avi', '.mov', '.mkv'], 
                       help="File extensions to convert (default: .webm .avi .mov .mkv)")
    parser.add_argument("--verbose", "-v", action="store_true", help="Enable verbose logging")
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Check for FFmpeg
    if not get_ffmpeg_path():
        logger.error("FFmpeg not found. Please install FFmpeg or ensure it's in your PATH.")
        sys.exit(1)
    
    if args.scan_recordings:
        converted_count = scan_recordings_directory()
        sys.exit(0 if converted_count >= 0 else 1)
    
    elif args.batch:
        converted_count = batch_convert_directory(args.batch, args.extensions)
        sys.exit(0 if converted_count >= 0 else 1)
    
    elif args.input_file:
        success = convert_single_file(args.input_file, args.output_file)
        sys.exit(0 if success else 1)
    
    else:
        parser.print_help()
        sys.exit(1)

if __name__ == "__main__":
    main()
