// Fixed Archive Playback with proper video codec support
import React, { useState, useEffect, useRef } from 'react';
import { 
  Video, 
  Play, 
  Pause, 
  Download, 
  Clock, 
  HardDrive, 
  ChevronLeft, 
  AlertCircle,
  RefreshCw
} from 'lucide-react';
import { useArchiveStore } from '../../store/archiveStore';
import './ArchivePlaybackContent.css';

const FixedArchivePlayback = ({ onSelectRecording, selectedRecordingId }) => {
  const {
    recordings,
    isLoading,
    error,
    loadRecordings,
    clearError
  } = useArchiveStore();

  const [selectedRecording, setSelectedRecording] = useState(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [videoError, setVideoError] = useState(null);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isConverting, setIsConverting] = useState(false);
  const [useConvertedVideo, setUseConvertedVideo] = useState(false);

  const videoRef = useRef(null);

  useEffect(() => {
    loadRecordings();
  }, [loadRecordings]);

  useEffect(() => {
    if (selectedRecordingId) {
      const recording = recordings.find(r => r.filename === selectedRecordingId);
      if (recording) {
        setSelectedRecording(recording);
      }
    }
  }, [selectedRecordingId, recordings]);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    clearError();
    try {
      await loadRecordings();
    } catch (err) {
      console.error('Failed to refresh:', err);
    } finally {
      setIsRefreshing(false);
    }
  };

  const handlePlayPause = () => {
    if (videoRef.current && !isConverting) {
      if (isPlaying) {
        videoRef.current.pause();
      } else {
        // Clear any previous errors before attempting to play
        setVideoError(null);
        videoRef.current.play().catch(err => {
          console.error('Play failed:', err);
          setVideoError('Unable to play video. The file may be corrupted or use an unsupported codec.');
        });
      }
    }
  };

  const handleVideoTimeUpdate = () => {
    if (videoRef.current) {
      setCurrentTime(videoRef.current.currentTime);
    }
  };

  const handleVideoLoadedMetadata = () => {
    if (videoRef.current) {
      setDuration(videoRef.current.duration);
      setVideoError(null);
    }
  };

  const handleVideoError = (e) => {
    console.error('Video error:', e.target.error);
    const errorCode = e.target.error?.code;
    let errorMessage = 'Video playback failed. ';

    switch (errorCode) {
      case 1: // MEDIA_ERR_ABORTED
        errorMessage += 'Playback was aborted.';
        break;
      case 2: // MEDIA_ERR_NETWORK
        errorMessage += 'Network error occurred.';
        break;
      case 3: // MEDIA_ERR_DECODE
        errorMessage += 'Video codec not supported or file is corrupted.';
        break;
      case 4: // MEDIA_ERR_SRC_NOT_SUPPORTED
        errorMessage += 'Video format not supported by browser.';
        break;
      default:
        errorMessage += 'Unknown error occurred.';
    }

    setVideoError(errorMessage);
  };

  const handleConvertVideo = async () => {
    if (!selectedRecording) return;

    setIsConverting(true);
    setVideoError(null);

    try {
      // Try to convert the video using the backend conversion endpoint
      const convertUrl = `/api/archive/convert/${selectedRecording.stream_id}/${selectedRecording.filename}`;
      console.log('Converting video:', convertUrl);

      // Test if conversion endpoint is available
      const response = await fetch(convertUrl, { method: 'HEAD' });
      if (response.ok) {
        setUseConvertedVideo(true);
        setVideoError(null);
        // Force video element to reload with converted source
        if (videoRef.current) {
          videoRef.current.load();
        }
      } else {
        throw new Error('Conversion service not available');
      }
    } catch (error) {
      console.error('Video conversion failed:', error);
      setVideoError('Video conversion failed. Please try downloading the file and playing it with VLC or another media player.');
    } finally {
      setIsConverting(false);
    }
  };

  const downloadRecording = (recording) => {
    if (!recording) return;

    // Use the proper API endpoint for downloading
    const downloadUrl = `/api/archive/stream/${recording.stream_id}/${recording.filename}`;

    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = recording.filename;
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const formatTime = (seconds) => {
    if (!seconds || isNaN(seconds)) return '00:00:00';
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const formatDate = (timestamp) => {
    return new Date(timestamp).toLocaleString();
  };

  const getVideoSources = (recording) => {
    if (!recording) return [];

    if (useConvertedVideo) {
      // Use converted video source
      return [
        `/api/archive/convert/${recording.stream_id}/${recording.filename}`
      ];
    }

    return [
      `/api/archive/stream/${recording.stream_id}/${recording.filename}`,
      `/backend/recordings/${recording.stream_id}/${recording.filename}`,
      `/recordings/${recording.stream_id}/${recording.filename}`,
      `./backend/recordings/${recording.stream_id}/${recording.filename}`
    ];
  };

  const handlePlayRecording = (recording) => {
    setSelectedRecording(recording);
    setVideoError(null);
    setUseConvertedVideo(false);
    setIsConverting(false);
    if (onSelectRecording) {
      onSelectRecording(recording.filename);
    }
  };

  if (selectedRecording) {
    return (
      <div className="archive-playback-content">
        <div className="recordings-container">
          <div className="flex items-center mb-4">
            <button
              onClick={() => setSelectedRecording(null)}
              className="flex items-center text-gray-600 hover:text-black mr-4"
            >
              <ChevronLeft className="w-5 h-5 mr-1" />
              Back to Recordings
            </button>
          </div>

          <div className="bg-white border border-gray-200 rounded-lg overflow-hidden mb-4">
            <div className="bg-gray-900 relative">
              {videoError ? (
                <div className="w-full h-64 md:h-96 flex items-center justify-center bg-gray-800 text-white">
                  <div className="text-center">
                    <AlertCircle className="w-12 h-12 mx-auto mb-4 text-red-400" />
                    <h3 className="text-lg font-semibold mb-2">Video Playback Error</h3>
                    <p className="text-sm text-gray-300 mb-4">{videoError}</p>
                    <p className="text-xs text-gray-400 mb-4">
                      This may be due to unsupported video encoding. Try converting the video or downloading the file.
                    </p>
                    <div className="space-x-3">
                      <button
                        onClick={handleConvertVideo}
                        disabled={isConverting}
                        className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50"
                      >
                        <RefreshCw className={`w-4 h-4 inline mr-2 ${isConverting ? 'animate-spin' : ''}`} />
                        {isConverting ? 'Converting...' : 'Convert Video'}
                      </button>
                      <button
                        onClick={() => downloadRecording(selectedRecording)}
                        className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                      >
                        <Download className="w-4 h-4 inline mr-2" />
                        Download File
                      </button>
                    </div>
                  </div>
                </div>
              ) : isConverting ? (
                <div className="w-full h-64 md:h-96 flex items-center justify-center bg-gray-800 text-white">
                  <div className="text-center">
                    <RefreshCw className="w-12 h-12 mx-auto mb-4 text-blue-400 animate-spin" />
                    <h3 className="text-lg font-semibold mb-2">Converting Video</h3>
                    <p className="text-sm text-gray-300">
                      Converting video to browser-compatible format. This may take a moment...
                    </p>
                  </div>
                </div>
              ) : (
                <video
                  ref={videoRef}
                  className="w-full h-64 md:h-96 object-cover"
                  onPlay={() => {
                    console.log('Video started playing');
                    setIsPlaying(true);
                  }}
                  onPause={() => {
                    console.log('Video paused');
                    setIsPlaying(false);
                  }}
                  onTimeUpdate={handleVideoTimeUpdate}
                  onLoadedMetadata={handleVideoLoadedMetadata}
                  onError={handleVideoError}
                  onCanPlay={() => {
                    console.log('Video can play - loaded successfully');
                    setVideoError(null);
                  }}
                  onLoadStart={() => {
                    console.log('Video load started');
                    setVideoError(null);
                  }}
                  controls
                  preload="metadata"
                >
                  {getVideoSources(selectedRecording).map((src, index) => (
                    <source key={index} src={src} type="video/mp4" />
                  ))}
                  Your browser does not support the video tag.
                </video>
              )}

            </div>

            <div className="p-4">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-lg font-semibold text-black">
                  {selectedRecording.stream_id || 'Unknown Camera'}
                </h3>
                <button
                  onClick={() => downloadRecording(selectedRecording)}
                  className="px-3 py-1 bg-blue-100 text-blue-600 rounded hover:bg-blue-200"
                >
                  <Download className="w-4 h-4 inline mr-1" />
                  Download
                </button>
              </div>
              <div className="grid grid-cols-3 gap-4 text-sm text-gray-600">
                <div className="flex items-center">
                  <Clock className="w-4 h-4 mr-1" />
                  {formatDate(selectedRecording.timestamp)}
                </div>
                <div className="flex items-center">
                  <Video className="w-4 h-4 mr-1" />
                  {formatTime(duration)}
                </div>
                <div className="flex items-center">
                  <HardDrive className="w-4 h-4 mr-1" />
                  {selectedRecording.size_bytes ? `${Math.round(selectedRecording.size_bytes / 1024)} KB` : 'Unknown Size'}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="archive-playback-content">
      <div className="recordings-container">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-2xl font-bold text-black">Archive Playback</h1>
          <button
            onClick={handleRefresh}
            disabled={isRefreshing}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
          >
            <RefreshCw className={`w-4 h-4 inline mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
            Refresh
          </button>
        </div>

        {isLoading && (
          <div className="loading-container">
            <div className="loading-spinner"></div>
            <h3>Loading Archive</h3>
            <p>Fetching recordings from all cameras...</p>
          </div>
        )}

        {error && (
          <div className="error-container">
            <AlertCircle className="w-12 h-12 text-red-500 mb-4" />
            <h3>Unable to Load Archive</h3>
            <p className="error-message">{error}</p>
            <button onClick={handleRefresh} className="retry-button">
              Try Again
            </button>
          </div>
        )}

        {!isLoading && !error && (
          <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
            {recordings.length === 0 ? (
              <div className="col-span-full">
                <div className="no-recordings">
                  <div className="no-recordings-icon">📁</div>
                  <h3>No archived recordings found</h3>
                  <p>No recordings are currently available from any camera.</p>
                  <button onClick={handleRefresh} className="refresh-button">
                    <RefreshCw className="refresh-icon" />
                    Refresh Archive
                  </button>
                </div>
              </div>
            ) : (
              recordings.map(recording => (
                <div
                  key={recording.filename}
                  className="bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-lg transition-shadow cursor-pointer"
                  onClick={() => handlePlayRecording(recording)}
                >
                  <div className="relative">
                    <div className="w-full h-32 bg-gray-100 flex items-center justify-center">
                      <Video className="w-8 h-8 text-gray-400" />
                    </div>
                    <div className="absolute bottom-2 right-2 bg-black bg-opacity-70 text-white px-2 py-1 rounded text-xs">
                      {recording.size_bytes ? `${Math.round(recording.size_bytes / 1024)} KB` : 'Unknown'}
                    </div>
                  </div>
                  <div className="p-3">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-semibold text-black text-sm">
                        {recording.stream_id || 'Unknown Camera'}
                      </h4>
                    </div>
                    <div className="text-xs text-gray-600 mb-1">
                      {formatDate(recording.timestamp)}
                    </div>
                    <div className="text-xs text-gray-500">
                      {recording.filename}
                    </div>
                    <div className="flex items-center justify-between mt-3">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handlePlayRecording(recording);
                        }}
                        className="text-xs px-3 py-1 bg-blue-100 text-blue-600 rounded hover:bg-blue-200"
                      >
                        <Play className="w-3 h-3 inline mr-1" />
                        Play
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          downloadRecording(recording);
                        }}
                        className="text-xs px-3 py-1 bg-gray-100 text-gray-600 rounded hover:bg-gray-200"
                      >
                        <Download className="w-3 h-3 inline mr-1" />
                        Download
                      </button>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default React.memo(FixedArchivePlayback);
